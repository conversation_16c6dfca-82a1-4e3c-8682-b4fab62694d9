<svg width="500" height="500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="lightGradient2" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#F0E68C;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#DAA520;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B8860B;stop-opacity:1" />
    </radialGradient>
    <radialGradient id="glowGradient2" cx="50%" cy="50%" r="60%">
      <stop offset="0%" style="stop-color:#FFFACD;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#FFFACD;stop-opacity:0" />
    </radialGradient>
  </defs>
  <rect width="500" height="500" fill="#1C1C1C"/>
  <circle cx="250" cy="250" r="180" fill="url(#glowGradient2)"/>
  <line x1="250" y1="80" x2="250" y2="170" stroke="#CD853F" stroke-width="5"/>
  <circle cx="250" cy="220" r="70" fill="url(#lightGradient2)" stroke="#CD853F" stroke-width="4"/>
  <circle cx="250" cy="220" r="50" fill="#FFFACD" opacity="0.8"/>
  <text x="250" y="360" font-family="Arial, sans-serif" font-size="18" fill="#DAA520" text-anchor="middle" font-weight="bold">Side Profile</text>
  <text x="250" y="385" font-family="Arial, sans-serif" font-size="14" fill="#F0E68C" text-anchor="middle">Adjustable Height</text>
</svg>
