<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="sofaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2F4F4F;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#696969;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A9A9A9;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="#F0F8FF"/>
  
  <!-- Sofa silhouette -->
  <rect x="80" y="120" width="240" height="80" fill="url(#sofaGradient)" rx="15"/>
  <rect x="100" y="140" width="200" height="60" fill="#708090" rx="12"/>
  <rect x="60" y="110" width="40" height="90" fill="url(#sofaGradient)" rx="10"/>
  <rect x="300" y="110" width="40" height="90" fill="url(#sofaGradient)" rx="10"/>
  
  <text x="200" y="40" font-family="Arial, sans-serif" font-size="24" fill="#2F4F4F" text-anchor="middle" font-weight="bold">Sofas</text>
  <text x="200" y="280" font-family="Arial, sans-serif" font-size="14" fill="#696969" text-anchor="middle">750+ Items</text>
</svg>
