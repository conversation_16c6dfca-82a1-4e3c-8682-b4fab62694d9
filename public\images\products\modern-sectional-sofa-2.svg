<svg width="500" height="500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="sofaGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#A9A9A9;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#696969;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2F4F4F;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="500" height="500" fill="url(#sofaGradient2)"/>
  <rect x="80" y="140" width="340" height="140" fill="#808080" rx="20"/>
  <rect x="320" y="100" width="140" height="220" fill="#808080" rx="20"/>
  <rect x="100" y="160" width="300" height="100" fill="#C0C0C0" rx="15"/>
  <rect x="340" y="120" width="100" height="180" fill="#C0C0C0" rx="15"/>
  <text x="250" y="360" font-family="Arial, sans-serif" font-size="18" fill="white" text-anchor="middle" font-weight="bold">Corner View</text>
  <text x="250" y="385" font-family="Arial, sans-serif" font-size="14" fill="#fff" text-anchor="middle">Modular Design</text>
</svg>
