<svg width="500" height="500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="circularGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#A9A9A9;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#696969;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2F4F4F;stop-opacity:1" />
    </radialGradient>
  </defs>
  <rect width="500" height="500" fill="url(#circularGradient)"/>
  <circle cx="250" cy="250" r="180" fill="#708090" stroke="#4682B4" stroke-width="8"/>
  <circle cx="250" cy="250" r="140" fill="#B0C4DE" stroke="#87CEEB" stroke-width="4"/>
  <text x="250" y="240" font-family="Arial, sans-serif" font-size="20" fill="white" text-anchor="middle" font-weight="bold">Circular Sofa Chair</text>
  <text x="250" y="270" font-family="Arial, sans-serif" font-size="16" fill="#fff" text-anchor="middle">360° Swivel Design</text>
</svg>
