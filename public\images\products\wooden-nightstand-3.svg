<svg width="500" height="500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="nightstandGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFEFD5;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F4A460;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DEB887;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="500" height="500" fill="url(#nightstandGradient3)"/>
  <rect x="100" y="60" width="300" height="380" fill="#F5DEB3" rx="20"/>
  <rect x="120" y="80" width="260" height="100" fill="#FFF8DC" rx="12"/>
  <rect x="120" y="200" width="260" height="100" fill="#FFF8DC" rx="12"/>
  <rect x="120" y="320" width="260" height="100" fill="#FFF8DC" rx="12"/>
  <circle cx="360" cy="130" r="12" fill="#8B4513"/>
  <circle cx="360" cy="250" r="12" fill="#8B4513"/>
  <text x="250" y="470" font-family="Arial, sans-serif" font-size="18" fill="#8B4513" text-anchor="middle" font-weight="bold">Side Detail</text>
</svg>
