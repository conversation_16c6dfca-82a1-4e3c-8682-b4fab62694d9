<svg width="500" height="500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="beanBagGradient2" cx="50%" cy="60%" r="60%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#6495ED;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4169E1;stop-opacity:1" />
    </radialGradient>
  </defs>
  <rect width="500" height="500" fill="#F0F8FF"/>
  <ellipse cx="250" cy="360" rx="170" ry="110" fill="url(#beanBagGradient2)"/>
  <ellipse cx="250" cy="330" rx="150" ry="90" fill="#B0E0E6"/>
  <ellipse cx="250" cy="310" rx="130" ry="70" fill="#E0F6FF"/>
  <text x="250" y="460" font-family="Arial, sans-serif" font-size="20" fill="#0000CD" text-anchor="middle" font-weight="bold">Side View</text>
  <text x="250" y="485" font-family="Arial, sans-serif" font-size="14" fill="#4169E1" text-anchor="middle">Flexible Shape</text>
</svg>
