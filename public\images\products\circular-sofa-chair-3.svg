<svg width="500" height="500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="circularGradient3" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#D3D3D3;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#A9A9A9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#808080;stop-opacity:1" />
    </radialGradient>
  </defs>
  <rect width="500" height="500" fill="url(#circularGradient3)"/>
  <circle cx="250" cy="250" r="170" fill="#C0C0C0" stroke="#696969" stroke-width="5"/>
  <circle cx="250" cy="250" r="130" fill="#F5F5F5" stroke="#DCDCDC" stroke-width="2"/>
  <text x="250" y="240" font-family="Arial, sans-serif" font-size="19" fill="#2F4F4F" text-anchor="middle" font-weight="bold">Detail View</text>
  <text x="250" y="270" font-family="Arial, sans-serif" font-size="15" fill="#2F4F4F" text-anchor="middle">Premium Fabric</text>
</svg>
