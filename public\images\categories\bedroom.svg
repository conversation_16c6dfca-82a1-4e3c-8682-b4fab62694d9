<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bedroomGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#DEB887;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#D2691E;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A0522D;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="#FFF8DC"/>
  
  <!-- Bed silhouette -->
  <rect x="80" y="140" width="240" height="100" fill="url(#bedroomGradient)" rx="12"/>
  <rect x="100" y="160" width="200" height="60" fill="#F4A460" rx="8"/>
  <rect x="60" y="120" width="30" height="120" fill="url(#bedroomGradient)" rx="8"/>
  <rect x="310" y="120" width="30" height="120" fill="url(#bedroomGradient)" rx="8"/>
  
  <!-- Nightstand -->
  <rect x="350" y="180" width="40" height="60" fill="#CD853F" rx="5"/>
  <rect x="355" y="185" width="30" height="20" fill="#F5DEB3" rx="3"/>
  <rect x="355" y="215" width="30" height="20" fill="#F5DEB3" rx="3"/>
  
  <text x="200" y="40" font-family="Arial, sans-serif" font-size="24" fill="#8B4513" text-anchor="middle" font-weight="bold">Bedroom</text>
  <text x="200" y="280" font-family="Arial, sans-serif" font-size="14" fill="#A0522D" text-anchor="middle">320+ Items</text>
</svg>
