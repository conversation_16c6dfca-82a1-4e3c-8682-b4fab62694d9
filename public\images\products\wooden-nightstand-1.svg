<svg width="500" height="500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="nightstandGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#DEB887;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#D2691E;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A0522D;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="500" height="500" fill="url(#nightstandGradient)"/>
  <rect x="150" y="100" width="200" height="300" fill="#CD853F" rx="10"/>
  <rect x="170" y="120" width="160" height="80" fill="#F4A460" rx="5"/>
  <rect x="170" y="220" width="160" height="80" fill="#F4A460" rx="5"/>
  <rect x="170" y="320" width="160" height="60" fill="#F4A460" rx="5"/>
  <circle cx="320" cy="160" r="8" fill="#8B4513"/>
  <circle cx="320" cy="260" r="8" fill="#8B4513"/>
  <text x="250" y="430" font-family="Arial, sans-serif" font-size="20" fill="white" text-anchor="middle" font-weight="bold">Wooden Nightstand</text>
  <text x="250" y="455" font-family="Arial, sans-serif" font-size="14" fill="#fff" text-anchor="middle">Compact Storage</text>
</svg>
