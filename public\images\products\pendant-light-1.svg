<svg width="500" height="500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="lightGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFFACD;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DAA520;stop-opacity:1" />
    </radialGradient>
    <radialGradient id="glowGradient" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#FFFFE0;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#FFFFE0;stop-opacity:0" />
    </radialGradient>
  </defs>
  <rect width="500" height="500" fill="#2F2F2F"/>
  <circle cx="250" cy="250" r="200" fill="url(#glowGradient)"/>
  <line x1="250" y1="50" x2="250" y2="150" stroke="#B8860B" stroke-width="4"/>
  <circle cx="250" cy="200" r="80" fill="url(#lightGradient)" stroke="#B8860B" stroke-width="3"/>
  <circle cx="250" cy="200" r="60" fill="#FFF8DC" opacity="0.7"/>
  <text x="250" y="350" font-family="Arial, sans-serif" font-size="20" fill="#FFD700" text-anchor="middle" font-weight="bold">Pendant Light Fixture</text>
  <text x="250" y="380" font-family="Arial, sans-serif" font-size="14" fill="#FFFACD" text-anchor="middle">Warm LED Lighting</text>
</svg>
