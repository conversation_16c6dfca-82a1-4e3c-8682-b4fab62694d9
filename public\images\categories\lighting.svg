<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="lightGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFFACD;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DAA520;stop-opacity:1" />
    </radialGradient>
    <radialGradient id="glowGradient" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#FFFFE0;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#FFFFE0;stop-opacity:0" />
    </radialGradient>
  </defs>
  <rect width="400" height="300" fill="#1C1C1C"/>
  
  <!-- Light fixture -->
  <circle cx="200" cy="150" r="120" fill="url(#glowGradient)"/>
  <line x1="200" y1="50" x2="200" y2="100" stroke="#B8860B" stroke-width="4"/>
  <circle cx="200" cy="130" r="40" fill="url(#lightGradient)" stroke="#B8860B" stroke-width="3"/>
  <circle cx="200" cy="130" r="30" fill="#FFF8DC" opacity="0.7"/>
  
  <text x="200" y="40" font-family="Arial, sans-serif" font-size="24" fill="#FFD700" text-anchor="middle" font-weight="bold">Lighting</text>
  <text x="200" y="280" font-family="Arial, sans-serif" font-size="14" fill="#FFFACD" text-anchor="middle">450+ Items</text>
</svg>
