<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="chairGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B4513;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#A0522D;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#CD853F;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="#F5F5DC"/>
  
  <!-- Chair silhouette -->
  <rect x="150" y="80" width="100" height="120" fill="url(#chairGradient)" rx="10"/>
  <rect x="160" y="90" width="80" height="100" fill="#D2691E" rx="8"/>
  <rect x="140" y="200" width="20" height="60" fill="url(#chairGradient)" rx="3"/>
  <rect x="240" y="200" width="20" height="60" fill="url(#chairGradient)" rx="3"/>
  <rect x="140" y="240" width="20" height="60" fill="url(#chairGradient)" rx="3"/>
  <rect x="240" y="240" width="20" height="60" fill="url(#chairGradient)" rx="3"/>
  
  <text x="200" y="40" font-family="Arial, sans-serif" font-size="24" fill="#8B4513" text-anchor="middle" font-weight="bold">Chairs</text>
  <text x="200" y="280" font-family="Arial, sans-serif" font-size="14" fill="#A0522D" text-anchor="middle">1,500+ Items</text>
</svg>
